{"mcpServers": {"pg-mcp-stdio": {"command": "go", "args": ["run", "main.go"], "cwd": "/opt/00_APPS/FINITY_AI/fin_mcp_server", "env": {"POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "postgres", "POSTGRES_DB": "postgres", "POSTGRES_SCHEMA": "fmcg", "MCP_TRANSPORT": "stdio"}}, "pg-mcp-http": {"transport": "streamable-http", "url": "http://localhost:8080/mcp", "description": "PostgreSQL MCP Server via StreamableHTTP"}}}