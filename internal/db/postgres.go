package db

import (
	"database/sql"
	"fmt"

	_ "github.com/lib/pq"
)

type Postgres struct {
	DB     *sql.DB
	Schema string
}

func NewPostgres(host, port, user, password, dbname, schema string) (*Postgres, error) {
	connStr := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname,
	)
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, err
	}

	// Set search_path to the specified schema if provided
	if schema != "" && schema != "public" {
		_, err = db.Exec(fmt.Sprintf("SET search_path TO %s, public", schema))
		if err != nil {
			return nil, fmt.Errorf("failed to set search_path: %v", err)
		}
	}

	return &Postgres{DB: db, Schema: schema}, nil
}
