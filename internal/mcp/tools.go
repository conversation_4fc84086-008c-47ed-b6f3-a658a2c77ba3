package mcp

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type Tools struct {
	DB     *sql.DB
	Schema string
}

// formatTableName formats table/view name with schema prefix if schema is specified
func (t *Tools) formatTableName(name string) string {
	if t.Schema != "" && t.Schema != "public" {
		return fmt.Sprintf("%s.%s", t.Schema, name)
	}
	return name
}

// Handler untuk query tabel
func (t *Tools) QueryTable(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	table, err := request.RequireString("table")
	if err != nil {
		return mcp.NewToolResultError("missing table parameter"), nil
	}

	limit := request.GetString("limit", "10")

	// Format table name with schema if specified
	fullTableName := t.formatTableName(table)
	q := fmt.Sprintf("SELECT * FROM %s LIMIT %s;", fullTableName, limit)
	rows, err := t.DB.QueryContext(ctx, q)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	cols, _ := rows.Columns()
	results := []map[string]any{}

	for rows.Next() {
		colsData := make([]any, len(cols))
		colsPtr := make([]any, len(cols))
		for i := range cols {
			colsPtr[i] = &colsData[i]
		}
		if err := rows.Scan(colsPtr...); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		rowMap := map[string]any{}
		for i, col := range cols {
			val := colsPtr[i].(*any)
			rowMap[col] = *val
		}
		results = append(results, rowMap)
	}

	result, err := mcp.NewToolResultJSON(map[string]any{"rows": results})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk query view
func (t *Tools) QueryView(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	view, err := request.RequireString("view")
	if err != nil {
		return mcp.NewToolResultError("missing view parameter"), nil
	}

	limit := request.GetString("limit", "10")

	// Format view name with schema if specified
	fullViewName := t.formatTableName(view)
	q := fmt.Sprintf("SELECT * FROM %s LIMIT %s;", fullViewName, limit)
	rows, err := t.DB.QueryContext(ctx, q)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	cols, _ := rows.Columns()
	results := []map[string]any{}

	for rows.Next() {
		colsData := make([]any, len(cols))
		colsPtr := make([]any, len(cols))
		for i := range cols {
			colsPtr[i] = &colsData[i]
		}
		if err := rows.Scan(colsPtr...); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		rowMap := map[string]any{}
		for i, col := range cols {
			val := colsPtr[i].(*any)
			rowMap[col] = *val
		}
		results = append(results, rowMap)
	}

	result, err := mcp.NewToolResultJSON(map[string]any{"rows": results})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Register semua tools ke server
func (t *Tools) Register(srv *server.MCPServer) {
	// Tool untuk query tabel
	queryTableTool := mcp.NewTool("query_table",
		mcp.WithDescription("Query data dari tabel PostgreSQL dengan limit"),
		mcp.WithString("table", mcp.Required()),
		mcp.WithString("limit"),
	)
	srv.AddTool(queryTableTool, t.QueryTable)

	// Tool untuk query view
	queryViewTool := mcp.NewTool("query_view",
		mcp.WithDescription("Query data dari view PostgreSQL dengan limit"),
		mcp.WithString("view", mcp.Required()),
		mcp.WithString("limit"),
	)
	srv.AddTool(queryViewTool, t.QueryView)
}
