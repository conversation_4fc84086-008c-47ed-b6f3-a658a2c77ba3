#!/bin/bash

# Script untuk menjalankan MCP Server dengan environment variables dari .env file

echo "Starting PostgreSQL MCP Server with environment configuration..."

# Load environment variables from .env file
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "Environment variables loaded successfully!"
else
    echo "Warning: .env file not found! Using default values..."
fi

# Display loaded configuration
echo ""
echo "Current Configuration:"
echo "======================"
echo "  MCP_TRANSPORT: ${MCP_TRANSPORT:-stdio}"
echo "  MCP_PORT: ${MCP_PORT:-8080}"
echo "  POSTGRES_HOST: ${POSTGRES_HOST:-localhost}"
echo "  POSTGRES_PORT: ${POSTGRES_PORT:-5432}"
echo "  POSTGRES_USER: ${POSTGRES_USER:-postgres}"
echo "  POSTGRES_DB: ${POSTGRES_DB:-postgres}"
echo "  POSTGRES_SCHEMA: ${POSTGRES_SCHEMA:-public}"
echo ""

# Build if binary doesn't exist
if [ ! -f "./fin_mcp_server" ]; then
    echo "Building application..."
    go build -o fin_mcp_server main.go
    if [ $? -ne 0 ]; then
        echo "Build failed!"
        exit 1
    fi
    echo "Build successful!"
fi

# Start the server
echo "Starting MCP Server..."
./fin_mcp_server
